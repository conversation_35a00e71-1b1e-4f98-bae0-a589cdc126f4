import Navbar from './components/layouts/Navbar';
import HeroSection from './components/sections/Hero';
import TreksSection from './components/sections/TreksSection';
import ReviewsSection from './components/sections/ReviewsSection';
import AboutSection from './components/sections/AboutSection';
import FooterSection from './components/sections/FooterSection';

export default function Home() {
  return (
    <div className="w-full">
      {/* Navbar - Full width */}
      <Navbar />

      {/* Main content container with max-width 80vw */}
      <div className="max-w-[80vw] mx-auto">
        <HeroSection />
        <TreksSection />
        <ReviewsSection />
      </div>

      {/* About Section - Full width */}
      <AboutSection />

      {/* Footer - Full width */}
      <FooterSection />
    </div>
  );
}