"use client"
import React from 'react';
import { MapPin, Star, Users, Award, Mountain, Camera } from 'lucide-react';

const OfficialGuideSection = () => {
  return (
    <section className="relative py-24 overflow-hidden bg-gradient-to-br from-gray-900 to-emerald-900">
      {/* Enhanced Background with Layered Effects */}
      <div className="absolute inset-0">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
          }}
        />
        
        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/70 to-emerald-900/40" />
        <div className="absolute inset-0 bg-gradient-to-br from-lime-400/10 via-green-500/15 to-emerald-600/20" />
        
        {/* Subtle Animated Pattern */}
        <div className="absolute inset-0 opacity-10 bg-[url('https://www.transparenttextures.com/patterns/45-degree-fabric-light.png')] animate-pattern-shift" />
      </div>

      {/* Animated Icons with Staggered Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-16 left-16 w-16 h-16 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float">
          <Mountain className="w-8 h-8 text-emerald-600" />
        </div>
        
        <div className="absolute top-24 right-24 w-14 h-14 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-slow">
          <Star className="w-7 h-7 text-amber-400 fill-current" />
        </div>
        
        <div className="absolute bottom-24 left-32 w-12 h-12 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float-delayed">
          <Camera className="w-6 h-6 text-blue-500" />
        </div>
        
        <div className="absolute bottom-32 right-16 w-18 h-18 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-delayed">
          <Award className="w-9 h-9 text-orange-500" />
        </div>
        
        <div className="absolute top-1/2 left-8 w-14 h-14 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-float-slow">
          <Users className="w-7 h-7 text-purple-500" />
        </div>
        
        <div className="absolute top-1/3 right-8 w-16 h-16 bg-white/95 rounded-full flex items-center justify-center shadow-lg animate-pulse-slower">
          <MapPin className="w-8 h-8 text-rose-500" />
        </div>
      </div>

      {/* Content Container */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center text-white">
          {/* Premium Badge */}
          <div className="inline-flex items-center bg-white/20 backdrop-blur-lg rounded-full px-6 py-3 mb-10 border border-white/10 shadow-lg">
            <Award className="w-6 h-6 mr-2 text-white" />
            <span className="text-base font-semibold tracking-wide">Official Guide Experience</span>
          </div>

          {/* Headline */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight tracking-tight">
            Discover Ourika with
            <span className="block bg-gradient-to-r from-lime-200 to-emerald-200 bg-clip-text text-transparent mt-2">
              Certified Local Guides
            </span>
          </h2>

          {/* Description */}
          <p className="text-xl mb-12 text-white/90 leading-relaxed max-w-3xl mx-auto font-light">
            Experience the authentic beauty of Ourika Valley with our certified local guides.
            From hidden waterfalls to traditional Berber villages, unlock exclusive access to
            the most breathtaking locations that only locals know.
          </p>

          {/* Features Grid */}
          <div className="grid md:grid-cols-3 gap-10 mb-14">
            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Star className="w-8 h-8 text-emerald-600 fill-current" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Expert Knowledge</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Deep understanding of local culture, history, and traditions</p>
            </div>

            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Mountain className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Hidden Trails</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Access to secret spots and breathtaking viewpoints</p>
            </div>

            <div className="flex flex-col items-center group">
              <div className="w-18 h-18 bg-white/95 rounded-full flex items-center justify-center mb-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Users className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-white font-semibold text-lg mb-3">Personal Touch</h3>
              <p className="text-white/80 text-center text-base leading-relaxed">Small groups for an intimate and personalized experience</p>
            </div>
          </div>

          {/* CTA Button */}
          <button className="bg-white hover:bg-gray-50 text-gray-900 px-10 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-emerald-700">
            Book Your Guide Today
          </button>
        </div>
      </div>

      {/* Custom Animation Styles */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }
        @keyframes pattern-shift {
          0% { background-position: 0 0; }
          100% { background-position: 50px 50px; }
        }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .animate-float-delayed { animation: float 7s ease-in-out infinite 1s; }
        .animate-float-slow { animation: float 8s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .animate-pulse-delayed { animation: pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite 1s; }
        .animate-pulse-slower { animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .animate-pattern-shift { animation: pattern-shift 30s linear infinite; }
      `}</style>
    </section>
  );
};

export default OfficialGuideSection;