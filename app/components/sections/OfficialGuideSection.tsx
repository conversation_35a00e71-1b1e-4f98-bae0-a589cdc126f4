"use client"
import React from 'react';
import { MapPin, Star, Users, Award, Mountain, Camera } from 'lucide-react';

const OfficialGuideSection = () => {
  return (
    <section className="relative py-20 overflow-hidden bg-gradient-to-br from-lime-400 via-green-400 to-emerald-500">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-white rounded-full"></div>
        <div className="absolute bottom-20 left-20 w-12 h-12 bg-white rounded-full"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 bg-white rounded-full"></div>
      </div>

      {/* Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Mountain Icon */}
        <div className="absolute top-16 left-16 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg animate-bounce">
          <Mountain className="w-8 h-8 text-green-600" />
        </div>
        
        {/* Star Icon */}
        <div className="absolute top-24 right-24 w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-lg animate-pulse">
          <Star className="w-7 h-7 text-yellow-500 fill-current" />
        </div>
        
        {/* Camera Icon */}
        <div className="absolute bottom-24 left-32 w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg animate-bounce delay-300">
          <Camera className="w-6 h-6 text-blue-600" />
        </div>
        
        {/* Award Icon */}
        <div className="absolute bottom-32 right-16 w-18 h-18 bg-white rounded-full flex items-center justify-center shadow-lg animate-pulse delay-500">
          <Award className="w-9 h-9 text-orange-500" />
        </div>
        
        {/* Users Icon */}
        <div className="absolute top-1/2 left-8 w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-lg animate-bounce delay-700">
          <Users className="w-7 h-7 text-purple-600" />
        </div>
        
        {/* Map Pin Icon */}
        <div className="absolute top-1/3 right-8 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg animate-pulse delay-1000">
          <MapPin className="w-8 h-8 text-red-500" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-white">
            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Award className="w-5 h-5 mr-2 text-white" />
              <span className="text-sm font-medium">Official Guide Experience</span>
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              Discover Ourika with
              <span className="block text-white drop-shadow-lg">
                Certified Local Guides
              </span>
            </h2>
            
            <p className="text-lg mb-8 text-white/90 leading-relaxed">
              Experience the authentic beauty of Ourika Valley with our certified local guides. 
              From hidden waterfalls to traditional Berber villages, unlock exclusive access to 
              the most breathtaking locations that only locals know.
            </p>
            
            <div className="space-y-4 mb-8">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-4">
                  <Star className="w-4 h-4 text-green-600 fill-current" />
                </div>
                <span className="text-white font-medium">Expert knowledge of local culture & history</span>
              </div>
              
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-4">
                  <Mountain className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-white font-medium">Access to hidden trails & secret spots</span>
              </div>
              
              <div className="flex items-center">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-4">
                  <Users className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-white font-medium">Small groups for personalized experience</span>
              </div>
            </div>
            
            <button className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl">
              Book Your Guide
            </button>
          </div>

          {/* Right Content - Phone Mockup */}
          <div className="relative">
            <div className="relative mx-auto w-80 h-[600px]">
              {/* Phone Frame */}
              <div className="absolute inset-0 bg-gray-900 rounded-[3rem] shadow-2xl">
                <div className="absolute inset-2 bg-white rounded-[2.5rem] overflow-hidden">
                  {/* Phone Screen Content */}
                  <div className="h-full bg-gradient-to-b from-green-50 to-white p-6">
                    {/* Status Bar */}
                    <div className="flex justify-between items-center mb-6 text-xs text-gray-600">
                      <span>9:41</span>
                      <div className="flex space-x-1">
                        <div className="w-4 h-2 bg-green-500 rounded-sm"></div>
                        <div className="w-4 h-2 bg-green-500 rounded-sm"></div>
                        <div className="w-4 h-2 bg-green-500 rounded-sm"></div>
                      </div>
                    </div>
                    
                    {/* App Header */}
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Ourika Guide</h3>
                      <p className="text-sm text-gray-600">Your Official Guide Experience</p>
                    </div>
                    
                    {/* Guide Profile Card */}
                    <div className="bg-white rounded-2xl p-4 shadow-lg mb-4">
                      <div className="flex items-center mb-3">
                        <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white font-bold">AH</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">Ahmed Hassan</h4>
                          <p className="text-xs text-gray-600">Certified Guide • 8 years exp</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                          <span className="text-sm font-medium">4.9</span>
                          <span className="text-xs text-gray-500 ml-1">(127 reviews)</span>
                        </div>
                        <span className="text-green-600 font-bold">$45/day</span>
                      </div>
                    </div>
                    
                    {/* Experience Highlights */}
                    <div className="space-y-3">
                      <div className="bg-green-50 rounded-xl p-3">
                        <div className="flex items-center">
                          <Mountain className="w-5 h-5 text-green-600 mr-2" />
                          <span className="text-sm font-medium text-gray-900">Waterfall Trek</span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">Hidden cascades & natural pools</p>
                      </div>
                      
                      <div className="bg-blue-50 rounded-xl p-3">
                        <div className="flex items-center">
                          <Users className="w-5 h-5 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-gray-900">Village Visit</span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">Traditional Berber culture</p>
                      </div>
                      
                      <div className="bg-orange-50 rounded-xl p-3">
                        <div className="flex items-center">
                          <Camera className="w-5 h-5 text-orange-600 mr-2" />
                          <span className="text-sm font-medium text-gray-900">Photo Spots</span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">Instagram-worthy locations</p>
                      </div>
                    </div>
                    
                    {/* Book Button */}
                    <button className="w-full bg-green-600 text-white py-3 rounded-xl font-semibold mt-6">
                      Book Now
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Phone Notch */}
              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-20 h-6 bg-gray-900 rounded-b-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OfficialGuideSection;
